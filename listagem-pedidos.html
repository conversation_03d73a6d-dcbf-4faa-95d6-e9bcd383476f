<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Listagem de Pedidos</title>
    
    <!-- Bootstrap 3.1 CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.1.1/css/bootstrap.min.css">
    
    <!-- Custom CSS -->
    <style>
        .pedido-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .entregador-foto {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #337ab7;
        }
        
        .status-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .status-pendente {
            background-color: #f0ad4e;
            color: white;
        }
        
        .status-entregue {
            background-color: #5cb85c;
            color: white;
        }
        
        .status-tentativa {
            background-color: #d9534f;
            color: white;
        }
        
        .prazo-badge {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        .prazo-d1 {
            background-color: #d9534f;
            color: white;
        }
        
        .prazo-d2 {
            background-color: #f0ad4e;
            color: white;
        }
        
        .info-row {
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .btn-actions {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .whatsapp-link {
            color: #25D366;
            text-decoration: none;
        }
        
        .whatsapp-link:hover {
            color: #128C7E;
            text-decoration: none;
        }
        
        .header-title {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #337ab7;
        }

        .checkbox-pedido {
            transform: scale(1.2);
            margin-right: 10px;
        }

        .acoes-lote {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }

        .contador-selecionados {
            font-weight: bold;
            color: #337ab7;
        }

        .btn-lote {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="header-title">
                    <h2><i class="glyphicon glyphicon-list-alt"></i> Listagem de Pedidos</h2>
                </div>
                
                <!-- Filtros -->
                <div class="panel panel-default">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3">
                                <select class="form-control" id="filtro-status">
                                    <option value="">Todos os Status</option>
                                    <option value="pendente">Pendente</option>
                                    <option value="tentativa">Em Tentativa</option>
                                    <option value="entregue">Entregue</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="filtro-prazo">
                                    <option value="">Todos os Prazos</option>
                                    <option value="D+1">D+1</option>
                                    <option value="D+2">D+2</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="busca-cliente" placeholder="Buscar por cliente...">
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary btn-block" onclick="filtrarPedidos()">
                                    <i class="glyphicon glyphicon-search"></i> Filtrar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ações em Lote -->
                <div class="acoes-lote" id="acoes-lote">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 style="margin-top: 0;">
                                <i class="glyphicon glyphicon-check"></i>
                                <span class="contador-selecionados" id="contador-selecionados">0</span> pedido(s) selecionado(s)
                            </h4>
                        </div>
                        <div class="col-md-6 text-right">
                            <button class="btn btn-danger btn-lote" onclick="removerSelecionados()">
                                <i class="glyphicon glyphicon-trash"></i> Remover Selecionados
                            </button>
                            <button class="btn btn-info btn-lote" onclick="imprimirEtiquetasSelecionadas()">
                                <i class="glyphicon glyphicon-print"></i> Imprimir Etiquetas
                            </button>
                            <button class="btn btn-default" onclick="desmarcarTodos()">
                                <i class="glyphicon glyphicon-unchecked"></i> Desmarcar Todos
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Lista de Pedidos -->
                <div id="lista-pedidos">
                    <!-- Os pedidos serão carregados aqui via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
    <!-- Bootstrap 3.1 JS -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.1.1/js/bootstrap.min.js"></script>
    
    <script>
        // Dados de exemplo dos pedidos
        const pedidosData = [
            {
                id: 'PED001',
                dataPedido: '2024-01-15',
                dataUltimaTentativa: '2024-01-16',
                numeroPedido: 'PED001',
                totalVolumes: 2,
                tentativasEntrega: 1,
                nomeCliente: 'João Silva',
                nomeRecebedor: 'Maria Silva',
                whatsappCliente: '11999887766',
                enderecoEntrega: 'Rua das Flores, 123 - Centro - São Paulo/SP',
                status: 'pendente',
                prazoEntrega: 'D+1',
                entregador: {
                    nome: 'Carlos Santos',
                    foto: 'https://via.placeholder.com/60x60/337ab7/ffffff?text=CS'
                }
            },
            {
                id: 'PED002',
                dataPedido: '2024-01-14',
                dataUltimaTentativa: '2024-01-15',
                numeroPedido: 'PED002',
                totalVolumes: 1,
                tentativasEntrega: 2,
                nomeCliente: 'Ana Costa',
                nomeRecebedor: 'Ana Costa',
                whatsappCliente: '11988776655',
                enderecoEntrega: 'Av. Paulista, 456 - Bela Vista - São Paulo/SP',
                status: 'tentativa',
                prazoEntrega: 'D+2',
                entregador: {
                    nome: 'Roberto Lima',
                    foto: 'https://via.placeholder.com/60x60/5cb85c/ffffff?text=RL'
                }
            },
            {
                id: 'PED003',
                dataPedido: '2024-01-13',
                dataUltimaTentativa: '2024-01-14',
                numeroPedido: 'PED003',
                totalVolumes: 3,
                tentativasEntrega: 0,
                nomeCliente: 'Pedro Oliveira',
                nomeRecebedor: 'Fernanda Oliveira',
                whatsappCliente: '11977665544',
                enderecoEntrega: 'Rua Augusta, 789 - Consolação - São Paulo/SP',
                status: 'entregue',
                prazoEntrega: 'D+1',
                entregador: {
                    nome: 'Lucas Ferreira',
                    foto: 'https://via.placeholder.com/60x60/f0ad4e/ffffff?text=LF'
                }
            }
        ];

        // Função para formatar data
        function formatarData(data) {
            const date = new Date(data);
            return date.toLocaleDateString('pt-BR');
        }

        // Função para obter classe do status
        function getStatusClass(status) {
            switch(status) {
                case 'pendente': return 'status-pendente';
                case 'tentativa': return 'status-tentativa';
                case 'entregue': return 'status-entregue';
                default: return 'status-pendente';
            }
        }

        // Função para obter texto do status
        function getStatusText(status) {
            switch(status) {
                case 'pendente': return 'Pendente';
                case 'tentativa': return 'Em Tentativa';
                case 'entregue': return 'Entregue';
                default: return 'Pendente';
            }
        }

        // Função para obter classe do prazo
        function getPrazoClass(prazo) {
            return prazo === 'D+1' ? 'prazo-d1' : 'prazo-d2';
        }

        // Função para renderizar pedidos
        function renderizarPedidos(pedidos) {
            const container = $('#lista-pedidos');
            container.empty();

            if (pedidos.length === 0) {
                container.html('<div class="alert alert-info">Nenhum pedido encontrado.</div>');
                return;
            }

            pedidos.forEach(pedido => {
                const pedidoHtml = `
                    <div class="pedido-card" data-pedido-id="${pedido.id}">
                        <div class="row">
                            <div class="col-md-1 text-center">
                                <div style="margin-top: 20px;">
                                    <input type="checkbox" class="checkbox-pedido" data-pedido-id="${pedido.id}" onchange="atualizarSelecao()">
                                </div>
                            </div>
                            <div class="col-md-2 text-center">
                                <img src="${pedido.entregador.foto}" alt="${pedido.entregador.nome}" class="entregador-foto">
                                <div style="margin-top: 5px; font-size: 12px; color: #666;">
                                    ${pedido.entregador.nome}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="row info-row">
                                    <div class="col-md-6">
                                        <span class="info-label">Pedido:</span> ${pedido.numeroPedido}
                                    </div>
                                    <div class="col-md-6">
                                        <span class="info-label">Data Pedido:</span> ${formatarData(pedido.dataPedido)}
                                    </div>
                                </div>
                                <div class="row info-row">
                                    <div class="col-md-6">
                                        <span class="info-label">Última Tentativa:</span> ${formatarData(pedido.dataUltimaTentativa)}
                                    </div>
                                    <div class="col-md-6">
                                        <span class="info-label">Volumes:</span> ${pedido.totalVolumes}
                                    </div>
                                </div>
                                <div class="row info-row">
                                    <div class="col-md-6">
                                        <span class="info-label">Tentativas:</span> ${pedido.tentativasEntrega}
                                    </div>
                                    <div class="col-md-6">
                                        <span class="info-label">Prazo:</span> 
                                        <span class="prazo-badge ${getPrazoClass(pedido.prazoEntrega)}">${pedido.prazoEntrega}</span>
                                    </div>
                                </div>
                                <div class="row info-row">
                                    <div class="col-md-12">
                                        <span class="info-label">Cliente:</span> ${pedido.nomeCliente}
                                        <a href="https://wa.me/55${pedido.whatsappCliente}" target="_blank" class="whatsapp-link">
                                            <i class="glyphicon glyphicon-phone"></i> ${pedido.whatsappCliente}
                                        </a>
                                    </div>
                                </div>
                                <div class="row info-row">
                                    <div class="col-md-12">
                                        <span class="info-label">Recebedor:</span> ${pedido.nomeRecebedor}
                                    </div>
                                </div>
                                <div class="row info-row">
                                    <div class="col-md-12">
                                        <span class="info-label">Endereço:</span> ${pedido.enderecoEntrega}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center" style="margin-bottom: 15px;">
                                    <span class="status-badge ${getStatusClass(pedido.status)}">
                                        ${getStatusText(pedido.status)}
                                    </span>
                                </div>
                                <div class="text-center">
                                    <button class="btn btn-danger btn-xs btn-actions" onclick="removerPedido('${pedido.id}')">
                                        <i class="glyphicon glyphicon-trash"></i> Remover
                                    </button>
                                    <button class="btn btn-info btn-xs btn-actions" onclick="imprimirEtiqueta('${pedido.id}')">
                                        <i class="glyphicon glyphicon-print"></i> Etiqueta
                                    </button>
                                    <button class="btn btn-success btn-xs btn-actions" onclick="abrirRastreio('${pedido.id}')">
                                        <i class="glyphicon glyphicon-map-marker"></i> Rastreio
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                container.append(pedidoHtml);
            });
        }

        // Funções de ação
        function removerPedido(pedidoId) {
            if (confirm('Tem certeza que deseja remover este pedido?')) {
                $(`[data-pedido-id="${pedidoId}"]`).fadeOut(300, function() {
                    $(this).remove();
                });
                alert('Pedido removido com sucesso!');
            }
        }

        function imprimirEtiqueta(pedidoId) {
            alert(`Imprimindo etiqueta do pedido ${pedidoId}`);
            // Aqui você implementaria a lógica de impressão
        }

        function abrirRastreio(pedidoId) {
            const url = `https://rastreio.exemplo.com/pedido/${pedidoId}`;
            window.open(url, '_blank');
        }

        // Função para atualizar seleção
        function atualizarSelecao() {
            const checkboxes = $('.checkbox-pedido:checked');
            const total = checkboxes.length;

            $('#contador-selecionados').text(total);

            if (total > 0) {
                $('#acoes-lote').slideDown();
            } else {
                $('#acoes-lote').slideUp();
            }
        }

        // Função para desmarcar todos
        function desmarcarTodos() {
            $('.checkbox-pedido').prop('checked', false);
            atualizarSelecao();
        }

        // Função para obter pedidos selecionados
        function obterPedidosSelecionados() {
            const selecionados = [];
            $('.checkbox-pedido:checked').each(function() {
                selecionados.push($(this).data('pedido-id'));
            });
            return selecionados;
        }

        // Função para remover pedidos selecionados
        function removerSelecionados() {
            const selecionados = obterPedidosSelecionados();

            if (selecionados.length === 0) {
                alert('Nenhum pedido selecionado!');
                return;
            }

            if (confirm(`Tem certeza que deseja remover ${selecionados.length} pedido(s) selecionado(s)?`)) {
                selecionados.forEach(pedidoId => {
                    $(`[data-pedido-id="${pedidoId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });
                });

                setTimeout(() => {
                    atualizarSelecao();
                    alert(`${selecionados.length} pedido(s) removido(s) com sucesso!`);
                }, 300);
            }
        }

        // Função para imprimir etiquetas selecionadas
        function imprimirEtiquetasSelecionadas() {
            const selecionados = obterPedidosSelecionados();

            if (selecionados.length === 0) {
                alert('Nenhum pedido selecionado!');
                return;
            }

            alert(`Imprimindo ${selecionados.length} etiqueta(s) dos pedidos: ${selecionados.join(', ')}`);
            // Aqui você implementaria a lógica de impressão em lote
        }

        // Função de filtro
        function filtrarPedidos() {
            const statusFiltro = $('#filtro-status').val();
            const prazoFiltro = $('#filtro-prazo').val();
            const clienteBusca = $('#busca-cliente').val().toLowerCase();

            let pedidosFiltrados = pedidosData.filter(pedido => {
                const matchStatus = !statusFiltro || pedido.status === statusFiltro;
                const matchPrazo = !prazoFiltro || pedido.prazoEntrega === prazoFiltro;
                const matchCliente = !clienteBusca ||
                    pedido.nomeCliente.toLowerCase().includes(clienteBusca) ||
                    pedido.nomeRecebedor.toLowerCase().includes(clienteBusca);

                return matchStatus && matchPrazo && matchCliente;
            });

            renderizarPedidos(pedidosFiltrados);
            // Resetar seleções após filtrar
            setTimeout(atualizarSelecao, 100);
        }

        // Inicializar página
        $(document).ready(function() {
            renderizarPedidos(pedidosData);

            // Event listeners para filtros
            $('#filtro-status, #filtro-prazo').change(filtrarPedidos);
            $('#busca-cliente').on('input', filtrarPedidos);
        });
    </script>
</body>
</html>
